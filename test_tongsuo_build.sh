#!/bin/bash

# Tongsuo集成测试构建脚本
# 用于测试Tongsuo在rctee TEE环境中的集成

set -e

echo "=========================================="
echo "    Tongsuo rctee TEE 集成测试构建"
echo "=========================================="

# 设置编译器路径
export COMPILER_PATH=/home/<USER>/codebase/trusty/prebuilts
echo "设置编译器路径: $COMPILER_PATH"

# 检查编译器是否存在
if [ ! -d "$COMPILER_PATH" ]; then
    echo "❌ 错误: 编译器路径不存在: $COMPILER_PATH"
    exit 1
fi

echo "✅ 编译器路径检查通过"

# 设置构建目标
BUILD_TARGET=imx8mp
echo "构建目标: $BUILD_TARGET"

# 检查Tongsuo库是否已编译
TONGSUO_LIB="opensource_libs/Tongsuo/build-arm64/lib/libcrypto.a"
if [ ! -f "$TONGSUO_LIB" ]; then
    echo "⚠️  Tongsuo库未找到，开始编译..."
    cd opensource_libs/Tongsuo
    export TARGET_ARCH=aarch64
    ./build_tongsuo_arm.sh
    cd ../..
    
    if [ ! -f "$TONGSUO_LIB" ]; then
        echo "❌ Tongsuo库编译失败"
        exit 1
    fi
    echo "✅ Tongsuo库编译完成"
else
    echo "✅ Tongsuo库已存在"
fi

# 检查Tongsuo构建文件是否存在
TONGSUO_SOURCES="opensource_libs/Tongsuo/sources.mk"
if [ ! -f "$TONGSUO_SOURCES" ]; then
    echo "⚠️  Tongsuo构建文件未找到，开始生成..."
    cd opensource_libs/Tongsuo
    python3 generate_build_files.py
    cd ../..
    
    if [ ! -f "$TONGSUO_SOURCES" ]; then
        echo "❌ Tongsuo构建文件生成失败"
        exit 1
    fi
    echo "✅ Tongsuo构建文件生成完成"
else
    echo "✅ Tongsuo构建文件已存在"
fi

# 检查测试TA是否存在
TEST_TA_MANIFEST="user/app/sample/tongsuo_test/manifest.json"
if [ ! -f "$TEST_TA_MANIFEST" ]; then
    echo "❌ 错误: Tongsuo测试TA未找到: $TEST_TA_MANIFEST"
    echo "请确保已创建测试TA文件"
    exit 1
fi
echo "✅ Tongsuo测试TA文件检查通过"

# 清理之前的构建
echo "清理之前的构建..."
rm -rf out/build-${BUILD_TARGET}/user/app/sample/tongsuo_test 2>/dev/null || true

# 开始构建
echo "开始构建rctee TEE系统（包含Tongsuo测试TA）..."
echo "构建命令: nice make ${BUILD_TARGET} -j 128"

# 记录构建开始时间
BUILD_START_TIME=$(date +%s)

# 执行构建
if nice make ${BUILD_TARGET} -j 128; then
    BUILD_END_TIME=$(date +%s)
    BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
    
    echo "✅ 构建成功完成！"
    echo "构建耗时: ${BUILD_DURATION}秒"
    
    # 检查输出文件
    OUTPUT_BIN="out/build-${BUILD_TARGET}/lk.bin"
    if [ -f "$OUTPUT_BIN" ]; then
        echo "✅ 输出文件生成成功: $OUTPUT_BIN"
        ls -lh "$OUTPUT_BIN"
        
        # 复制到目标位置
        TARGET_BIN="/home/<USER>/codebase/nxp-plus/vendor/nxp/fsl-proprietary/uboot-firmware/imx8m/tee-imx8mp.bin"
        if cp "$OUTPUT_BIN" "$TARGET_BIN" 2>/dev/null; then
            echo "✅ 文件复制成功: $TARGET_BIN"
            ls -lh "$TARGET_BIN"
        else
            echo "⚠️  文件复制失败（可能目标目录不存在）"
        fi
    else
        echo "❌ 输出文件未生成"
        exit 1
    fi
    
    # 检查Tongsuo测试TA是否被包含
    TEST_TA_BIN="out/build-${BUILD_TARGET}/user/app/sample/tongsuo_test/tongsuo_test.elf"
    if [ -f "$TEST_TA_BIN" ]; then
        echo "✅ Tongsuo测试TA编译成功: $TEST_TA_BIN"
        ls -lh "$TEST_TA_BIN"
        
        # 检查TA中是否包含Tongsuo符号
        if nm "$TEST_TA_BIN" | grep -q "TONGSUO_"; then
            echo "✅ 测试TA包含Tongsuo符号"
            echo "Tongsuo符号示例:"
            nm "$TEST_TA_BIN" | grep "TONGSUO_" | head -5
        else
            echo "⚠️  测试TA中未找到Tongsuo符号"
        fi
    else
        echo "❌ Tongsuo测试TA编译失败"
        exit 1
    fi
    
else
    echo "❌ 构建失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "           构建测试总结"
echo "=========================================="
echo "✅ Tongsuo库编译: 成功"
echo "✅ 构建文件生成: 成功"
echo "✅ rctee TEE系统构建: 成功"
echo "✅ Tongsuo测试TA编译: 成功"
echo "✅ 输出文件生成: 成功"
echo ""
echo "🎉 Tongsuo集成测试构建完成！"
echo ""
echo "下一步:"
echo "1. 将生成的TEE镜像烧录到设备"
echo "2. 启动设备并运行Tongsuo测试TA"
echo "3. 检查测试结果验证集成是否成功"
echo ""
echo "测试TA UUID: 8aaaf200-2450-11e4-abe2-0002a5d5c51b"
echo "=========================================="
