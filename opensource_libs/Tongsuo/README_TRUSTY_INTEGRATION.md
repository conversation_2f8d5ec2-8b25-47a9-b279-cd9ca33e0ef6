# Tongsuo Trusty集成指南

本文档说明如何将Tongsuo加密库集成到rctee TEE系统中。

## 概述

Tongsuo是一个提供现代密码学算法的开源密码库，特别支持中国商用密码算法（SM2/SM3/SM4）。本集成参考了BoringSSL在Trusty中的集成方式，为rctee TEE系统提供了完整的密码学支持。

## 集成特性

### 1. 核心特性
- **纯C实现**: 禁用汇编优化，确保最大兼容性
- **符号前缀**: 所有符号都有`TONGSUO_`前缀，避免与系统OpenSSL冲突
- **TEE优化**: 专门为TEE环境配置，禁用不需要的功能
- **OpenSSL 1.1.1兼容**: 兼容OpenSSL 1.1.1 API

### 2. 支持的算法
- **哈希算法**: MD5, SHA-1, SHA-2系列, SM3
- **对称加密**: AES, DES, 3DES, SM4
- **非对称加密**: RSA, DSA, ECDSA, SM2
- **消息认证**: HMAC, CMAC
- **密钥派生**: PBKDF2, HKDF

### 3. Trusty适配
- **随机数生成**: 对接Trusty硬件随机数生成器
- **内存管理**: 安全的内存分配和清零
- **错误处理**: 适配TEE环境的错误处理机制

## 文件结构

```
opensource_libs/Tongsuo/
├── build_tongsuo_arm.sh          # ARM交叉编译脚本
├── generate_build_files.py       # 构建文件生成脚本
├── sources.mk                    # 源文件列表
├── crypto-sources.mk             # Trusty构建配置
├── rules.mk                      # Trusty模块规则
├── crypto/
│   ├── rand/trusty_rand.c        # Trusty随机数适配
│   └── mem/trusty_mem.c          # Trusty内存管理适配
└── test_integration.c            # 集成测试程序

build/tools/tongsuo/
└── rules.mk                      # 主机构建工具
```

## 编译步骤

### 1. 编译ARM64库
```bash
cd opensource_libs/Tongsuo
export TARGET_ARCH=aarch64
./build_tongsuo_arm.sh
```

### 2. 生成构建文件
```bash
python3 generate_build_files.py
```

### 3. 在Trusty项目中使用
在你的Trusty模块的`rules.mk`中添加：
```makefile
MODULE_DEPS += opensource_libs/Tongsuo
```

## 使用示例

### 1. SM3哈希
```c
#include <openssl/sm3.h>

unsigned char hash[SM3_DIGEST_LENGTH];
SM3_CTX ctx;
SM3_Init(&ctx);
SM3_Update(&ctx, data, data_len);
SM3_Final(hash, &ctx);
```

### 2. SM4加密
```c
#include <openssl/sm4.h>

SM4_KEY key;
SM4_set_key(key_data, &key);
SM4_encrypt(plaintext, ciphertext, &key);
SM4_decrypt(ciphertext, decrypted, &key);
```

### 3. 随机数生成
```c
#include <openssl/rand.h>

unsigned char random_bytes[32];
RAND_bytes(random_bytes, sizeof(random_bytes));
```

### 4. EVP接口
```c
#include <openssl/evp.h>

EVP_MD_CTX *ctx = EVP_MD_CTX_new();
EVP_DigestInit_ex(ctx, EVP_sm3(), NULL);
EVP_DigestUpdate(ctx, data, data_len);
EVP_DigestFinal_ex(ctx, hash, &hash_len);
EVP_MD_CTX_free(ctx);
```

## 编译配置

### 编译标志
```makefile
-DTONGSUO_IMPLEMENTATION
-D__TEE__ -D__TRUSTY__
-DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS
-DOPENSSL_API_COMPAT=0x10101000L
-DSYMBOL_PREFIX=TONGSUO_
-DOPENSSL_NO_ASM
```

### 包含路径
```makefile
-I opensource_libs/Tongsuo/include
```

### 链接库
```makefile
opensource_libs/Tongsuo/build-arm64/lib/libcrypto.a
```

## 测试验证

运行集成测试：
```bash
# 编译测试程序（需要在Trusty环境中）
gcc -I include test_integration.c -L build-arm64/lib -lcrypto -o test_integration

# 运行测试
./test_integration
```

## 注意事项

1. **内存管理**: 在TEE环境中要特别注意内存泄漏
2. **错误处理**: 所有API调用都应该检查返回值
3. **随机数**: 依赖Trusty的硬件随机数生成器
4. **符号冲突**: 使用符号前缀避免与其他OpenSSL版本冲突
5. **性能**: 纯C实现可能比汇编优化版本慢，但更稳定

## 故障排除

### 编译错误
- 确保工具链路径正确设置
- 检查`COMPILER_PATH`环境变量
- 验证所有依赖文件存在

### 运行时错误
- 检查随机数生成器是否可用
- 验证内存分配是否成功
- 确保符号前缀配置正确

### 性能问题
- 考虑启用特定的优化算法
- 检查是否正确禁用了不需要的功能
- 验证编译优化标志

## 参考资料

- [Tongsuo官方文档](https://www.tongsuo.net/)
- [OpenSSL 1.1.1 API文档](https://www.openssl.org/docs/man1.1.1/)
- [Trusty TEE文档](https://source.android.com/security/trusty)
- [BoringSSL集成参考](https://boringssl.googlesource.com/boringssl/)
