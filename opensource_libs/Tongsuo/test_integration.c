/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo集成测试程序
 * 验证Tongsuo在Trusty环境中的基本功能
 */

#include <stdio.h>
#include <string.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/sm2.h>

int test_sm3_hash(void) {
    printf("测试SM3哈希算法...\n");
    
    const char *data = "Hello, Tongsuo!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    if (!SM3_Init(&ctx)) {
        printf("SM3_Init失败\n");
        return 0;
    }
    
    if (!SM3_Update(&ctx, data, strlen(data))) {
        printf("SM3_Update失败\n");
        return 0;
    }
    
    if (!SM3_Final(hash, &ctx)) {
        printf("SM3_Final失败\n");
        return 0;
    }
    
    printf("SM3哈希结果: ");
    for (int i = 0; i < SM3_DIGEST_LENGTH; i++) {
        printf("%02x", hash[i]);
    }
    printf("\n");
    
    return 1;
}

int test_sm4_encryption(void) {
    printf("测试SM4加密算法...\n");
    
    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    const unsigned char plaintext[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    SM4_KEY sm4_key;
    if (SM4_set_key(key, &sm4_key) != 0) {
        printf("SM4_set_key失败\n");
        return 0;
    }
    
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    if (memcmp(plaintext, decrypted, 16) != 0) {
        printf("SM4加密/解密验证失败\n");
        return 0;
    }
    
    printf("SM4加密/解密测试通过\n");
    return 1;
}

int test_random_generation(void) {
    printf("测试随机数生成...\n");
    
    unsigned char random_bytes[32];
    
    if (!RAND_bytes(random_bytes, sizeof(random_bytes))) {
        printf("RAND_bytes失败\n");
        return 0;
    }
    
    printf("随机数: ");
    for (int i = 0; i < 16; i++) {  // 只显示前16字节
        printf("%02x", random_bytes[i]);
    }
    printf("...\n");
    
    return 1;
}

int test_evp_digest(void) {
    printf("测试EVP摘要接口...\n");
    
    const char *data = "Tongsuo EVP Test";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    
    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) {
        printf("EVP_MD_CTX_new失败\n");
        return 0;
    }
    
    if (!EVP_DigestInit_ex(ctx, EVP_sm3(), NULL)) {
        printf("EVP_DigestInit_ex失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    if (!EVP_DigestUpdate(ctx, data, strlen(data))) {
        printf("EVP_DigestUpdate失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    if (!EVP_DigestFinal_ex(ctx, hash, &hash_len)) {
        printf("EVP_DigestFinal_ex失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    EVP_MD_CTX_free(ctx);
    
    printf("EVP SM3摘要结果: ");
    for (unsigned int i = 0; i < hash_len && i < 16; i++) {
        printf("%02x", hash[i]);
    }
    printf("...\n");
    
    return 1;
}

int main(void) {
    printf("=== Tongsuo Trusty集成测试 ===\n");
    
    int tests_passed = 0;
    int total_tests = 4;
    
    if (test_random_generation()) {
        tests_passed++;
    }
    
    if (test_sm3_hash()) {
        tests_passed++;
    }
    
    if (test_sm4_encryption()) {
        tests_passed++;
    }
    
    if (test_evp_digest()) {
        tests_passed++;
    }
    
    printf("\n=== 测试结果 ===\n");
    printf("通过: %d/%d\n", tests_passed, total_tests);
    
    if (tests_passed == total_tests) {
        printf("✅ 所有测试通过！Tongsuo集成成功。\n");
        return 0;
    } else {
        printf("❌ 部分测试失败。\n");
        return 1;
    }
}
