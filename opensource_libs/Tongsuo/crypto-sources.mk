# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Now used only by Trusty - Tongsuo integration
LOCAL_ADDITIONAL_DEPENDENCIES += $(LOCAL_PATH)/sources.mk
include $(LOCAL_PATH)/sources.mk

# Tongsuo编译标志
LOCAL_CFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto
LOCAL_CFLAGS += -Wno-unused-parameter -Wno-array-bounds -Wno-error
LOCAL_CFLAGS += -DTONGSUO_ANDROID_SYSTEM -D__TEE__
LOCAL_CFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS
LOCAL_CFLAGS += -DOPENSSL_API_COMPAT=0x10101000L
LOCAL_CFLAGS += -DOPENSSL_USE_NODELETE -DOPENSSL_PIC
LOCAL_CFLAGS += -DNDEBUG -fPIC -Os -ffunction-sections -fdata-sections

# 汇编文件标志
LOCAL_ASFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto
LOCAL_ASFLAGS += -Wno-unused-parameter

# 源文件
LOCAL_SRC_FILES += $(crypto_sources)

# Trusty适配文件
ifeq ($(TARGET_ARCH),arm64)
LOCAL_SRC_FILES += $(crypto_sources_trusty)
endif
ifeq ($(TARGET_ARCH),arm)
LOCAL_SRC_FILES += $(crypto_sources_trusty)
endif

# 符号前缀支持
LOCAL_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_
