#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tongsuo构建文件生成脚本
参考BoringSSL的generate_build_files.py，为Tongsuo生成Trusty构建系统所需的文件
"""

import os
import re
import subprocess
import sys


def find_c_files(directory, filter_func):
    """查找C源文件"""
    c_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.c'):
                rel_path = os.path.relpath(os.path.join(root, file), '.')
                if filter_func(rel_path):
                    c_files.append(rel_path)
    return sorted(c_files)


def find_header_files(directory, filter_func):
    """查找头文件"""
    h_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.h'):
                rel_path = os.path.relpath(os.path.join(root, file), '.')
                if filter_func(rel_path):
                    h_files.append(rel_path)
    return sorted(h_files)


def no_tests_filter(path):
    """过滤掉测试文件"""
    return not ('test' in path or 'fuzz' in path or 'example' in path)


def extract_sources_from_makefile():
    """从Makefile中提取源文件列表"""
    crypto_sources = []

    # 读取Makefile，提取OBJS变量中的源文件
    try:
        with open('Makefile', 'r') as f:
            content = f.read()

        # 查找所有libcrypto相关的.o文件，转换为.c文件
        obj_pattern = r'crypto/[^/\s]+/libcrypto-lib-[^/\s]+\.o|crypto/libcrypto-lib-[^/\s]+\.o'
        obj_files = re.findall(obj_pattern, content)

        for obj_file in obj_files:
            # 从libcrypto-lib-xxx.o转换为xxx.c
            base_name = os.path.basename(obj_file)
            if base_name.startswith('libcrypto-lib-'):
                source_name = base_name.replace('libcrypto-lib-', '').replace('.o', '.c')
                dir_name = os.path.dirname(obj_file)
                c_file = os.path.join(dir_name, source_name)
                if os.path.exists(c_file):
                    crypto_sources.append(c_file)

    except FileNotFoundError:
        print("警告: 未找到Makefile，使用目录扫描方式")

    # 如果从Makefile提取失败或文件太少，使用目录扫描
    if len(crypto_sources) < 50:
        print("从Makefile提取的文件较少，使用目录扫描方式")
        crypto_sources = find_c_files('crypto', no_tests_filter)

    return sorted(list(set(crypto_sources)))


class AndroidMakefileWriter:
    """生成Android.mk格式的构建文件"""
    
    def __init__(self):
        self.header = """# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file is created by generate_build_files.py. Do not edit manually.

"""

    def print_variable_section(self, out, name, files):
        """输出变量定义段"""
        out.write('%s := \\\n' % name)
        for f in sorted(files):
            out.write('  %s\\\n' % f)
        out.write('\n')

    def write_files(self, files):
        """写入sources.mk文件"""
        with open('sources.mk', 'w+') as makefile:
            makefile.write(self.header)
            self.print_variable_section(makefile, 'crypto_sources', files['crypto'])
            self.print_variable_section(makefile, 'crypto_headers', files['crypto_headers'])


def main():
    """主函数"""
    print("正在生成Tongsuo构建文件...")
    
    # 提取源文件
    crypto_sources = extract_sources_from_makefile()
    crypto_headers = find_header_files('include/openssl', lambda x: True)
    
    # 过滤掉不需要的文件
    crypto_sources = [f for f in crypto_sources if no_tests_filter(f)]
    
    files = {
        'crypto': crypto_sources,
        'crypto_headers': crypto_headers,
    }
    
    print(f"找到 {len(crypto_sources)} 个加密库源文件")
    print(f"找到 {len(crypto_headers)} 个头文件")
    
    # 生成构建文件
    writer = AndroidMakefileWriter()
    writer.write_files(files)
    
    print("构建文件生成完成:")
    print("  - sources.mk")


if __name__ == '__main__':
    main()
