# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Tongsuo主机构建工具

HOST_LIB_NAME := tongsuo

TONGSUO_DIR := opensource_libs/Tongsuo

HOST_ARCH := $(shell uname -m)
ifneq ($(HOST_ARCH),x86_64)
$(error Unsupported host architecture: $(HOST_ARCH), expected x86_64)
endif

include $(TONGSUO_DIR)/sources.mk
HOST_LIB_SRCS := \
	$(addprefix opensource_libs/Tongsuo/,$(crypto_sources)) \

HOST_INCLUDE_DIRS += \
	opensource_libs/Tongsuo/include \

# Tongsuo主机编译标志
HOST_CFLAGS += -DTONGSUO_IMPLEMENTATION
HOST_CFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS
HOST_CFLAGS += -DOPENSSL_API_COMPAT=0x10101000L
HOST_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_
HOST_CFLAGS += -D__TEE__ -DNDEBUG
HOST_CFLAGS += -fPIC -Os

include make/host_lib.mk

HOST_ARCH :=
