#include <stddef.h>
#include <stdbool.h>
#include <stdint.h>
#include <user_ta_header.h>
#include "user_ta_header_defines.h"
#include <tee_ta_api.h>

// 函数声明
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size);

/* 使用宏定义的值赋给全局变量 */
const uuid_t uuid = TA_UUID;
const uint32_t used_perms = TA_ALLOW_CONNECT_PERM;

const struct user_ta_property ta_props[] = {
/*
 * Extended propietary properties, name of properties must not begin with
 * "gpd."
 */
#ifdef TA_CURRENT_TA_EXT_PROPERTIES
	TA_CURRENT_TA_EXT_PROPERTIES
#endif
};

const size_t ta_num_props = sizeof(ta_props) / sizeof(ta_props[0]);

/* RCTEE回调函数 */
int RCTEE_OnCall(uint32_t cmd,
                 uint8_t* in_buf,
                 size_t in_buf_size,
                 uint8_t** out_buf,
                 size_t* out_buf_size) {
    return tongsuo_test_handle_cmd(cmd, in_buf, in_buf_size, out_buf, out_buf_size);
}
