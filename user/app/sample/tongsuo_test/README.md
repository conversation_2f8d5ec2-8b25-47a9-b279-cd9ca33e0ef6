# Tongsuo测试TA

这是一个用于验证Tongsuo加密库在rctee TEE环境中集成的测试TA。

## 概述

该测试TA验证以下功能：
- 随机数生成（RAND_bytes）
- SM3哈希算法
- SM4对称加密
- AES对称加密  
- SHA256哈希算法
- EVP接口（使用SM3）

## 文件结构

```
user/app/sample/tongsuo_test/
├── manifest.json          # TA清单文件
├── rules.mk               # 构建规则
├── tongsuo_test_ta.c      # 测试TA主程序
└── README.md              # 本文档
```

## TA信息

- **UUID**: `8aaaf200-2450-11e4-abe2-0002a5d5c51b`
- **最小栈大小**: 4KB
- **最小堆大小**: 16KB
- **重启策略**: 退出后不重启

## 构建方法

### 1. 确保Tongsuo库已编译
```bash
cd opensource_libs/Tongsuo
export TARGET_ARCH=aarch64
./build_tongsuo_arm.sh
```

### 2. 生成构建文件
```bash
cd opensource_libs/Tongsuo
python3 generate_build_files.py
```

### 3. 添加到构建配置
在 `kernel/rctee/platform/nxp/imx8/project/imx8mp.mk` 中添加：
```makefile
BUNDLED_TA_TASKS += \
    user/app/sample/tongsuo_test \
```

### 4. 执行构建
```bash
# 使用项目根目录的构建脚本
./local_build.sh

# 或者使用专门的测试构建脚本
./test_tongsuo_build.sh
```

## 测试内容

### 1. 随机数生成测试
- 测试 `RAND_bytes()` 函数
- 生成32字节随机数
- 验证随机数生成是否成功

### 2. SM3哈希测试
- 测试SM3哈希算法
- 对测试字符串进行哈希计算
- 验证哈希结果

### 3. SM4加密测试
- 测试SM4对称加密算法
- 使用固定密钥和明文
- 验证加密/解密的一致性

### 4. AES加密测试
- 测试AES-128对称加密算法
- 使用固定密钥和明文
- 验证加密/解密的一致性

### 5. SHA256哈希测试
- 测试SHA256哈希算法
- 对测试字符串进行哈希计算
- 验证哈希结果

### 6. EVP接口测试
- 测试EVP高级接口
- 使用EVP接口调用SM3算法
- 验证接口的正确性

## 预期输出

成功运行时，测试TA会输出类似以下内容：

```
==========================================
    Tongsuo rctee TEE 集成测试
==========================================

=== 测试随机数生成 ===
随机数: 1a2b3c4d5e6f7890...
✅ 随机数生成测试通过

=== 测试SM3哈希算法 ===
输入数据: Hello, Tongsuo in rctee TEE!
SM3哈希: 9f2fcc7c89177b2c...
✅ SM3哈希测试通过

=== 测试SM4加密算法 ===
SM4密钥: 0123456789abcdef...
明文: 0123456789abcdef...
密文: 681edf34d206965e...
解密: 0123456789abcdef...
✅ SM4加密/解密测试通过

=== 测试AES加密算法 ===
AES密钥: 2b7e151628aed2a6...
明文: 3243f6a8885a308d...
密文: 3925841d02dc09fb...
解密: 3243f6a8885a308d...
✅ AES加密/解密测试通过

=== 测试SHA256哈希算法 ===
输入数据: Tongsuo SHA256 test in rctee
SHA256哈希: 5d41402abc4b2a76...
✅ SHA256哈希测试通过

=== 测试EVP接口 ===
输入数据: EVP interface test with SM3
EVP SM3哈希: 8f434346648f6b96...
✅ EVP接口测试通过

==========================================
           测试结果汇总
==========================================
通过测试: 6/6
🎉 所有测试通过！Tongsuo集成成功！
✅ rctee TEE环境中的Tongsuo功能正常
==========================================
```

## 故障排除

### 编译错误
1. **找不到Tongsuo头文件**
   - 确保Tongsuo库已正确编译
   - 检查 `opensource_libs/Tongsuo/include` 目录是否存在

2. **链接错误**
   - 确保 `libcrypto.a` 已生成
   - 检查 `rules.mk` 中的依赖配置

3. **符号未定义**
   - 确保使用了正确的符号前缀
   - 检查Tongsuo编译时是否启用了符号前缀

### 运行时错误
1. **随机数生成失败**
   - 检查Trusty随机数生成器是否可用
   - 验证 `trusty_rand.c` 适配层是否正确

2. **内存分配失败**
   - 增加TA的堆大小配置
   - 检查内存泄漏

3. **算法初始化失败**
   - 检查Tongsuo库是否正确初始化
   - 验证算法是否在编译时启用

## 依赖项

- `user/base/lib/libc-trusty`: C标准库
- `user/base/lib/rctee`: rctee TEE库
- `opensource_libs/Tongsuo`: Tongsuo加密库

## 注意事项

1. 该测试TA仅用于验证集成，不应在生产环境中使用
2. 测试使用的密钥和数据都是固定的，仅用于功能验证
3. 在实际应用中应使用安全的密钥管理和随机数生成
4. 测试结果会输出到系统日志，注意不要在生产环境中泄露敏感信息
