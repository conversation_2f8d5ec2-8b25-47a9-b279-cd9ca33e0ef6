/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#include <tee_api_types.h>
#include <tee_api_defines.h>
#include <tee_ta_api.h>
#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>
#include <unistd.h>

// 暂时注释掉Tongsuo头文件，先测试基本构建
// #include <openssl/evp.h>
// #include <openssl/rand.h>
// #include <openssl/sm3.h>
// #include <openssl/sm4.h>
// #include <openssl/aes.h>
// #include <openssl/sha.h>

#define TONGSUO_PORT "8aaaf200-2450-11e4-abe2-0002a5d5c51b"
#define TLOG_TAG "tongsuo_test"
#define TONGSUO_MAX_BUFFER_LENGTH 1024

#define TEE_TEST_CMD_BASIC_TEST                1
#define TEE_TEST_CMD_MEMORY_TEST              2
#define TEE_TEST_CMD_CRYPTO_TEST              3

static void print_hex(const char* label, const unsigned char* data, size_t len) {
    TLOGI("%s: ", label);
    for (size_t i = 0; i < len; i++) {
        TLOGI("%02x", data[i]);
    }
    TLOGI("\n");
}

static TEE_Result test_basic_functionality(void) {
    TLOGI("=== 测试基本功能 ===\n");

    // 简单的内存分配测试
    void *test_ptr = malloc(1024);
    if (!test_ptr) {
        TLOGE("❌ 内存分配失败\n");
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    // 写入测试数据
    memset(test_ptr, 0xAA, 1024);

    // 验证数据
    unsigned char *data = (unsigned char *)test_ptr;
    for (int i = 0; i < 1024; i++) {
        if (data[i] != 0xAA) {
            TLOGE("❌ 内存数据验证失败\n");
            free(test_ptr);
            return TEE_ERROR_GENERIC;
        }
    }

    free(test_ptr);
    TLOGI("✅ 基本功能测试通过\n");
    return TEE_SUCCESS;
}

static TEE_Result test_memory_functionality(void) {
    TLOGI("=== 测试内存管理功能 ===\n");

    // 测试多次内存分配和释放
    for (int i = 0; i < 10; i++) {
        size_t size = 512 + i * 64;
        void *ptr = malloc(size);
        if (!ptr) {
            TLOGE("❌ 内存分配失败，大小: %zu\n", size);
            return TEE_ERROR_OUT_OF_MEMORY;
        }

        // 写入测试模式
        memset(ptr, 0x55 + i, size);

        // 验证数据
        unsigned char *data = (unsigned char *)ptr;
        unsigned char expected = 0x55 + i;
        for (size_t j = 0; j < size; j++) {
            if (data[j] != expected) {
                TLOGE("❌ 内存数据验证失败\n");
                free(ptr);
                return TEE_ERROR_GENERIC;
            }
        }

        free(ptr);
    }

    TLOGI("✅ 内存管理功能测试通过\n");
    return TEE_SUCCESS;
}

static TEE_Result test_crypto_placeholder(void) {
    TLOGI("=== 测试加密功能占位符 ===\n");

    // 简单的数据处理测试，模拟加密操作
    unsigned char test_data[32];
    unsigned char processed_data[32];

    // 初始化测试数据
    for (int i = 0; i < 32; i++) {
        test_data[i] = i;
    }

    // 简单的"加密"操作（XOR）
    for (int i = 0; i < 32; i++) {
        processed_data[i] = test_data[i] ^ 0xAA;
    }

    // "解密"操作
    for (int i = 0; i < 32; i++) {
        processed_data[i] = processed_data[i] ^ 0xAA;
    }

    // 验证数据
    if (memcmp(test_data, processed_data, 32) != 0) {
        TLOGE("❌ 数据处理验证失败\n");
        return TEE_ERROR_GENERIC;
    }

    TLOGI("✅ 加密功能占位符测试通过\n");
    TLOGI("注意: 这是占位符测试，Tongsuo库集成后将替换为真实的加密测试\n");
    return TEE_SUCCESS;
}

// TA命令处理函数
static TEE_Result handle_basic_test(uint32_t param_types, TEE_Param params[4]) {
    (void)param_types;
    (void)params;

    TLOGI("处理基本测试命令\n");
    return test_basic_functionality();
}

static TEE_Result handle_memory_test(uint32_t param_types, TEE_Param params[4]) {
    (void)param_types;
    (void)params;

    TLOGI("处理内存测试命令\n");
    return test_memory_functionality();
}

static TEE_Result handle_crypto_test(uint32_t param_types, TEE_Param params[4]) {
    (void)param_types;
    (void)params;

    TLOGI("处理加密测试命令\n");
    return test_crypto_placeholder();
}

// TA入口点函数
TEE_Result TA_CreateEntryPoint(void) {
    TLOGI("Tongsuo测试TA创建\n");
    return TEE_SUCCESS;
}

void TA_DestroyEntryPoint(void) {
    TLOGI("Tongsuo测试TA销毁\n");
}

TEE_Result TA_OpenSessionEntryPoint(uint32_t param_types,
                                   TEE_Param params[4],
                                   void **sess_ctx) {
    (void)param_types;
    (void)params;
    (void)sess_ctx;

    TLOGI("Tongsuo测试TA会话打开\n");
    return TEE_SUCCESS;
}

void TA_CloseSessionEntryPoint(void *sess_ctx) {
    (void)sess_ctx;
    TLOGI("Tongsuo测试TA会话关闭\n");
}

TEE_Result TA_InvokeCommandEntryPoint(void *sess_ctx,
                                     uint32_t cmd_id,
                                     uint32_t param_types,
                                     TEE_Param params[4]) {
    (void)sess_ctx;

    TLOGI("Tongsuo测试TA命令调用: %u\n", cmd_id);

    switch (cmd_id) {
    case TEE_TEST_CMD_BASIC_TEST:
        return handle_basic_test(param_types, params);

    case TEE_TEST_CMD_MEMORY_TEST:
        return handle_memory_test(param_types, params);

    case TEE_TEST_CMD_CRYPTO_TEST:
        return handle_crypto_test(param_types, params);

    default:
        TLOGE("未知命令: %u\n", cmd_id);
        return TEE_ERROR_BAD_PARAMETERS;
    }
}

// 简单的main函数用于独立测试
int main(void) {
    TLOGI("\n");
    TLOGI("========================================\n");
    TLOGI("    Tongsuo rctee TEE 集成测试\n");
    TLOGI("========================================\n");
    TLOGI("测试Tongsuo加密库在rctee TEE环境中的功能\n");

    int tests_passed = 0;
    int total_tests = 3;

    // 执行各项测试
    if (test_basic_functionality() == TEE_SUCCESS) tests_passed++;
    if (test_memory_functionality() == TEE_SUCCESS) tests_passed++;
    if (test_crypto_placeholder() == TEE_SUCCESS) tests_passed++;

    TLOGI("========================================\n");
    TLOGI("           测试结果汇总\n");
    TLOGI("========================================\n");
    TLOGI("通过测试: %d/%d\n", tests_passed, total_tests);

    if (tests_passed == total_tests) {
        TLOGI("🎉 所有测试通过！基础功能正常！\n");
        TLOGI("✅ rctee TEE环境中的基础功能正常\n");
        TLOGI("注意: Tongsuo库集成后将添加更多加密测试\n");
    } else {
        TLOGE("❌ 部分测试失败，请检查配置\n");
    }

    TLOGI("========================================\n");

    return (tests_passed == total_tests) ? 0 : 1;
}
