/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_syscalls.h>
#include <err.h>

// Tongsuo头文件
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/aes.h>
#include <openssl/sha.h>

#define LOG_TAG "tongsuo_test"

static void print_hex(const char* label, const unsigned char* data, size_t len) {
    printf("%s: ", label);
    for (size_t i = 0; i < len; i++) {
        printf("%02x", data[i]);
    }
    printf("\n");
}

static int test_random_generation(void) {
    printf("=== 测试随机数生成 ===\n");
    
    unsigned char random_bytes[32];
    
    if (!RAND_bytes(random_bytes, sizeof(random_bytes))) {
        printf("❌ RAND_bytes失败\n");
        return 0;
    }
    
    print_hex("随机数", random_bytes, 16);  // 只显示前16字节
    printf("✅ 随机数生成测试通过\n\n");
    return 1;
}

static int test_sm3_hash(void) {
    printf("=== 测试SM3哈希算法 ===\n");
    
    const char *test_data = "Hello, Tongsuo in rctee TEE!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    if (!SM3_Init(&ctx)) {
        printf("❌ SM3_Init失败\n");
        return 0;
    }
    
    if (!SM3_Update(&ctx, test_data, strlen(test_data))) {
        printf("❌ SM3_Update失败\n");
        return 0;
    }
    
    if (!SM3_Final(hash, &ctx)) {
        printf("❌ SM3_Final失败\n");
        return 0;
    }
    
    printf("输入数据: %s\n", test_data);
    print_hex("SM3哈希", hash, SM3_DIGEST_LENGTH);
    printf("✅ SM3哈希测试通过\n\n");
    return 1;
}

static int test_sm4_encryption(void) {
    printf("=== 测试SM4加密算法 ===\n");
    
    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    const unsigned char plaintext[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    SM4_KEY sm4_key;
    if (SM4_set_key(key, &sm4_key) != 0) {
        printf("❌ SM4_set_key失败\n");
        return 0;
    }
    
    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    SM4_decrypt(ciphertext, decrypted, &sm4_key);
    
    print_hex("SM4密钥", key, 16);
    print_hex("明文", plaintext, 16);
    print_hex("密文", ciphertext, 16);
    print_hex("解密", decrypted, 16);
    
    if (memcmp(plaintext, decrypted, 16) != 0) {
        printf("❌ SM4加密/解密验证失败\n");
        return 0;
    }
    
    printf("✅ SM4加密/解密测试通过\n\n");
    return 1;
}

static int test_aes_encryption(void) {
    printf("=== 测试AES加密算法 ===\n");
    
    const unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };
    
    const unsigned char plaintext[16] = {
        0x32, 0x43, 0xf6, 0xa8, 0x88, 0x5a, 0x30, 0x8d,
        0x31, 0x31, 0x98, 0xa2, 0xe0, 0x37, 0x07, 0x34
    };
    
    unsigned char ciphertext[16];
    unsigned char decrypted[16];
    
    AES_KEY aes_key;
    if (AES_set_encrypt_key(key, 128, &aes_key) != 0) {
        printf("❌ AES_set_encrypt_key失败\n");
        return 0;
    }
    
    AES_encrypt(plaintext, ciphertext, &aes_key);
    
    if (AES_set_decrypt_key(key, 128, &aes_key) != 0) {
        printf("❌ AES_set_decrypt_key失败\n");
        return 0;
    }
    
    AES_decrypt(ciphertext, decrypted, &aes_key);
    
    print_hex("AES密钥", key, 16);
    print_hex("明文", plaintext, 16);
    print_hex("密文", ciphertext, 16);
    print_hex("解密", decrypted, 16);
    
    if (memcmp(plaintext, decrypted, 16) != 0) {
        printf("❌ AES加密/解密验证失败\n");
        return 0;
    }
    
    printf("✅ AES加密/解密测试通过\n\n");
    return 1;
}

static int test_sha256_hash(void) {
    printf("=== 测试SHA256哈希算法 ===\n");
    
    const char *test_data = "Tongsuo SHA256 test in rctee";
    unsigned char hash[SHA256_DIGEST_LENGTH];
    
    SHA256_CTX ctx;
    if (!SHA256_Init(&ctx)) {
        printf("❌ SHA256_Init失败\n");
        return 0;
    }
    
    if (!SHA256_Update(&ctx, test_data, strlen(test_data))) {
        printf("❌ SHA256_Update失败\n");
        return 0;
    }
    
    if (!SHA256_Final(hash, &ctx)) {
        printf("❌ SHA256_Final失败\n");
        return 0;
    }
    
    printf("输入数据: %s\n", test_data);
    print_hex("SHA256哈希", hash, SHA256_DIGEST_LENGTH);
    printf("✅ SHA256哈希测试通过\n\n");
    return 1;
}

static int test_evp_interface(void) {
    printf("=== 测试EVP接口 ===\n");
    
    const char *test_data = "EVP interface test with SM3";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    
    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) {
        printf("❌ EVP_MD_CTX_new失败\n");
        return 0;
    }
    
    if (!EVP_DigestInit_ex(ctx, EVP_sm3(), NULL)) {
        printf("❌ EVP_DigestInit_ex失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    if (!EVP_DigestUpdate(ctx, test_data, strlen(test_data))) {
        printf("❌ EVP_DigestUpdate失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    if (!EVP_DigestFinal_ex(ctx, hash, &hash_len)) {
        printf("❌ EVP_DigestFinal_ex失败\n");
        EVP_MD_CTX_free(ctx);
        return 0;
    }
    
    EVP_MD_CTX_free(ctx);
    
    printf("输入数据: %s\n", test_data);
    print_hex("EVP SM3哈希", hash, hash_len);
    printf("✅ EVP接口测试通过\n\n");
    return 1;
}

int main(void) {
    printf("\n");
    printf("========================================\n");
    printf("    Tongsuo rctee TEE 集成测试\n");
    printf("========================================\n");
    printf("测试Tongsuo加密库在rctee TEE环境中的功能\n\n");
    
    int tests_passed = 0;
    int total_tests = 6;
    
    // 执行各项测试
    if (test_random_generation()) tests_passed++;
    if (test_sm3_hash()) tests_passed++;
    if (test_sm4_encryption()) tests_passed++;
    if (test_aes_encryption()) tests_passed++;
    if (test_sha256_hash()) tests_passed++;
    if (test_evp_interface()) tests_passed++;
    
    printf("========================================\n");
    printf("           测试结果汇总\n");
    printf("========================================\n");
    printf("通过测试: %d/%d\n", tests_passed, total_tests);
    
    if (tests_passed == total_tests) {
        printf("🎉 所有测试通过！Tongsuo集成成功！\n");
        printf("✅ rctee TEE环境中的Tongsuo功能正常\n");
    } else {
        printf("❌ 部分测试失败，请检查集成配置\n");
    }
    
    printf("========================================\n\n");
    
    return (tests_passed == total_tests) ? 0 : 1;
}
