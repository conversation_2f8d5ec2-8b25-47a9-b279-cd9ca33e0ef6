/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>
#include <unistd.h>

// Tongsuo加密库头文件
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/aes.h>
#include <openssl/sha.h>
#include <openssl/err.h>
#include <openssl/ec.h>
#include <openssl/sm2.h>
#include <openssl/pem.h>

#define TONGSUO_PORT "8aaaf200-2450-11e4-abe2-0002a5d5c51b"
#define TLOG_TAG "tongsuo_test"
#define TONGSUO_MAX_BUFFER_LENGTH 1024

#define TEE_TEST_CMD_BASIC_TEST                1
#define TEE_TEST_CMD_MEMORY_TEST              2
#define TEE_TEST_CMD_SM3_HASH_TEST            3
#define TEE_TEST_CMD_SM4_ENCRYPT_TEST         4
#define TEE_TEST_CMD_RANDOM_TEST              5
#define TEE_TEST_CMD_SM2_ENCRYPT_TEST         6

// 函数声明
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size);

// 辅助函数：打印十六进制数据
static void print_hex(const char *label, const unsigned char *data, size_t len) {
    TLOGI("%s (%zu bytes): ", label, len);
    for (size_t i = 0; i < len; i++) {
        if (i > 0 && i % 16 == 0) {
            TLOGI("\n");
        }
        TLOGI("%02x ", data[i]);
    }
    TLOGI("\n");
}

static void print_hex(const char* label, const unsigned char* data, size_t len) {
    TLOGI("%s: ", label);
    for (size_t i = 0; i < len; i++) {
        TLOGI("%02x", data[i]);
    }
    TLOGI("\n");
}

static int test_basic_functionality(void) {
    TLOGI("=== 测试基本功能 ===\n");

    // 简单的内存分配测试
    void *test_ptr = malloc(1024);
    if (!test_ptr) {
        TLOGE("❌ 内存分配失败\n");
        return ERR_NO_MEMORY;
    }

    // 写入测试数据
    memset(test_ptr, 0xAA, 1024);

    // 验证数据
    unsigned char *data = (unsigned char *)test_ptr;
    for (int i = 0; i < 1024; i++) {
        if (data[i] != 0xAA) {
            TLOGE("❌ 内存数据验证失败\n");
            free(test_ptr);
            return ERR_GENERIC;
        }
    }

    free(test_ptr);
    TLOGI("✅ 基本功能测试通过\n");
    return TEE_SUCCESS;
}

static int test_memory_functionality(void) {
    TLOGI("=== 测试内存管理功能 ===\n");

    // 测试多次内存分配和释放
    for (int i = 0; i < 10; i++) {
        size_t size = 512 + i * 64;
        void *ptr = malloc(size);
        if (!ptr) {
            TLOGE("❌ 内存分配失败，大小: %zu\n", size);
            return ERR_NO_MEMORY;
        }

        // 写入测试模式
        memset(ptr, 0x55 + i, size);

        // 验证数据
        unsigned char *data = (unsigned char *)ptr;
        unsigned char expected = 0x55 + i;
        for (size_t j = 0; j < size; j++) {
            if (data[j] != expected) {
                TLOGE("❌ 内存数据验证失败\n");
                free(ptr);
                return ERR_GENERIC;
            }
        }

        free(ptr);
    }

    TLOGI("✅ 内存管理功能测试通过\n");
    return TEE_SUCCESS;
}

static int test_sm3_hash(void) {
    TLOGI("=== 测试SM3哈希算法 ===\n");

    const char *test_data = "Tongsuo SM3 test in rctee TEE";
    unsigned char hash[SM3_DIGEST_LENGTH];

    SM3_CTX ctx;
    if (!SM3_Init(&ctx)) {
        TLOGE("❌ SM3_Init失败\n");
        return ERR_GENERIC;
    }

    if (!SM3_Update(&ctx, test_data, strlen(test_data))) {
        TLOGE("❌ SM3_Update失败\n");
        return ERR_GENERIC;
    }

    if (!SM3_Final(hash, &ctx)) {
        TLOGE("❌ SM3_Final失败\n");
        return ERR_GENERIC;
    }

    TLOGI("输入数据: %s\n", test_data);
    print_hex("SM3哈希", hash, SM3_DIGEST_LENGTH);
    TLOGI("✅ SM3哈希测试通过\n");
    return TEE_SUCCESS;
}

static int test_sm4_encryption(void) {
    TLOGI("=== 测试SM4对称加密算法 ===\n");

    const unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };

    const unsigned char plaintext[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };

    unsigned char ciphertext[16];
    unsigned char decrypted[16];

    SM4_KEY sm4_key;
    if (SM4_set_key(key, &sm4_key) != 0) {
        TLOGE("❌ SM4_set_key失败\n");
        return ERR_GENERIC;
    }

    SM4_encrypt(plaintext, ciphertext, &sm4_key);
    SM4_decrypt(ciphertext, decrypted, &sm4_key);

    print_hex("SM4密钥", key, 16);
    print_hex("明文", plaintext, 16);
    print_hex("密文", ciphertext, 16);
    print_hex("解密", decrypted, 16);

    if (memcmp(plaintext, decrypted, 16) != 0) {
        TLOGE("❌ SM4加密/解密验证失败\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ SM4加密/解密测试通过\n");
    return TEE_SUCCESS;
}

static int test_random_generation(void) {
    TLOGI("=== 测试随机数生成 ===\n");

    unsigned char random_bytes[32];

    if (RAND_bytes(random_bytes, sizeof(random_bytes)) != 1) {
        TLOGE("❌ RAND_bytes失败\n");
        return ERR_GENERIC;
    }

    print_hex("随机数", random_bytes, sizeof(random_bytes));

    // 简单检查：确保不是全零
    bool all_zero = true;
    for (size_t i = 0; i < sizeof(random_bytes); i++) {
        if (random_bytes[i] != 0) {
            all_zero = false;
            break;
        }
    }

    if (all_zero) {
        TLOGE("❌ 随机数生成失败（全零）\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ 随机数生成测试通过\n");
    return TEE_SUCCESS;
}

static int test_sm2_encryption(void) {
    TLOGI("=== 测试SM2非对称加密算法 ===\n");

    EC_KEY *key = NULL;
    EVP_PKEY *pkey = NULL;
    EVP_PKEY_CTX *pctx = NULL;
    const char *plaintext = "SM2 test message in rctee TEE";
    unsigned char *ciphertext = NULL;
    unsigned char *decrypted = NULL;
    size_t ciphertext_len = 0;
    size_t decrypted_len = 0;
    int ret = ERR_GENERIC;

    // 创建SM2密钥
    key = EC_KEY_new_by_curve_name(NID_sm2);
    if (!key) {
        TLOGE("❌ EC_KEY_new_by_curve_name失败\n");
        goto cleanup;
    }

    if (!EC_KEY_generate_key(key)) {
        TLOGE("❌ EC_KEY_generate_key失败\n");
        goto cleanup;
    }

    // 转换为EVP_PKEY
    pkey = EVP_PKEY_new();
    if (!pkey) {
        TLOGE("❌ EVP_PKEY_new失败\n");
        goto cleanup;
    }

    if (!EVP_PKEY_assign_EC_KEY(pkey, key)) {
        TLOGE("❌ EVP_PKEY_assign_EC_KEY失败\n");
        goto cleanup;
    }
    key = NULL; // 现在由pkey管理

    // 设置SM2算法ID
    if (!EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2)) {
        TLOGE("❌ EVP_PKEY_set_alias_type失败\n");
        goto cleanup;
    }

    // 创建加密上下文
    pctx = EVP_PKEY_CTX_new(pkey, NULL);
    if (!pctx) {
        TLOGE("❌ EVP_PKEY_CTX_new失败\n");
        goto cleanup;
    }

    // 初始化加密
    if (EVP_PKEY_encrypt_init(pctx) <= 0) {
        TLOGE("❌ EVP_PKEY_encrypt_init失败\n");
        goto cleanup;
    }

    // 获取密文长度
    if (EVP_PKEY_encrypt(pctx, NULL, &ciphertext_len,
                        (const unsigned char*)plaintext, strlen(plaintext)) <= 0) {
        TLOGE("❌ EVP_PKEY_encrypt获取长度失败\n");
        goto cleanup;
    }

    // 分配密文缓冲区
    ciphertext = malloc(ciphertext_len);
    if (!ciphertext) {
        TLOGE("❌ 密文缓冲区分配失败\n");
        goto cleanup;
    }

    // 执行加密
    if (EVP_PKEY_encrypt(pctx, ciphertext, &ciphertext_len,
                        (const unsigned char*)plaintext, strlen(plaintext)) <= 0) {
        TLOGE("❌ EVP_PKEY_encrypt加密失败\n");
        goto cleanup;
    }

    TLOGI("明文: %s\n", plaintext);
    print_hex("SM2密文", ciphertext, ciphertext_len);

    // 重新初始化解密
    EVP_PKEY_CTX_free(pctx);
    pctx = EVP_PKEY_CTX_new(pkey, NULL);
    if (!pctx) {
        TLOGE("❌ 解密上下文创建失败\n");
        goto cleanup;
    }

    if (EVP_PKEY_decrypt_init(pctx) <= 0) {
        TLOGE("❌ EVP_PKEY_decrypt_init失败\n");
        goto cleanup;
    }

    // 获取解密后长度
    if (EVP_PKEY_decrypt(pctx, NULL, &decrypted_len, ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ EVP_PKEY_decrypt获取长度失败\n");
        goto cleanup;
    }

    // 分配解密缓冲区
    decrypted = malloc(decrypted_len + 1); // +1 for null terminator
    if (!decrypted) {
        TLOGE("❌ 解密缓冲区分配失败\n");
        goto cleanup;
    }

    // 执行解密
    if (EVP_PKEY_decrypt(pctx, decrypted, &decrypted_len, ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ EVP_PKEY_decrypt解密失败\n");
        goto cleanup;
    }

    decrypted[decrypted_len] = '\0'; // 添加字符串结束符
    TLOGI("解密结果: %s\n", (char*)decrypted);

    // 验证解密结果
    if (strlen(plaintext) != decrypted_len ||
        memcmp(plaintext, decrypted, decrypted_len) != 0) {
        TLOGE("❌ SM2加密/解密验证失败\n");
        goto cleanup;
    }

    TLOGI("✅ SM2加密/解密测试通过\n");
    ret = TEE_SUCCESS;

cleanup:
    if (ciphertext) free(ciphertext);
    if (decrypted) free(decrypted);
    if (pctx) EVP_PKEY_CTX_free(pctx);
    if (pkey) EVP_PKEY_free(pkey);
    if (key) EC_KEY_free(key);

    return ret;
}

// rctee TA命令处理函数
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size) {

    TLOGI("Tongsuo测试TA命令调用: %u\n", cmd);

    switch (cmd) {
    case TEE_TEST_CMD_BASIC_TEST:
        TLOGI("处理基本测试命令\n");
        return test_basic_functionality();

    case TEE_TEST_CMD_MEMORY_TEST:
        TLOGI("处理内存测试命令\n");
        return test_memory_functionality();

    case TEE_TEST_CMD_SM3_HASH_TEST:
        TLOGI("处理SM3哈希测试命令\n");
        return test_sm3_hash();

    case TEE_TEST_CMD_SM4_ENCRYPT_TEST:
        TLOGI("处理SM4加密测试命令\n");
        return test_sm4_encryption();

    case TEE_TEST_CMD_RANDOM_TEST:
        TLOGI("处理随机数测试命令\n");
        return test_random_generation();

    case TEE_TEST_CMD_SM2_ENCRYPT_TEST:
        TLOGI("处理SM2加密测试命令\n");
        return test_sm2_encryption();

    default:
        TLOGE("未知命令: %u\n", cmd);
        return ERR_INVALID_ARGS;
    }
}

// rctee TA入口点函数
int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo测试TA客户端连接\n");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    TLOGI("Tongsuo测试TA客户端断开连接\n");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo测试TA初始化\n");
    return TEE_SUCCESS;
}

// 注意: main函数由libutee库提供，这里不需要定义
