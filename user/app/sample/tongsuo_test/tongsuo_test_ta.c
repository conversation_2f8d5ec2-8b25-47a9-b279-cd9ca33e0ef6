/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>
#include <unistd.h>

// 暂时注释掉Tongsuo头文件，先测试基本构建
// #include <openssl/evp.h>
// #include <openssl/rand.h>
// #include <openssl/sm3.h>
// #include <openssl/sm4.h>
// #include <openssl/aes.h>
// #include <openssl/sha.h>

#define TONGSUO_PORT "8aaaf200-2450-11e4-abe2-0002a5d5c51b"
#define TLOG_TAG "tongsuo_test"
#define TONGSUO_MAX_BUFFER_LENGTH 1024

#define TEE_TEST_CMD_BASIC_TEST                1
#define TEE_TEST_CMD_MEMORY_TEST              2
#define TEE_TEST_CMD_CRYPTO_TEST              3

// 函数声明
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size);

static void print_hex(const char* label, const unsigned char* data, size_t len) {
    TLOGI("%s: ", label);
    for (size_t i = 0; i < len; i++) {
        TLOGI("%02x", data[i]);
    }
    TLOGI("\n");
}

static int test_basic_functionality(void) {
    TLOGI("=== 测试基本功能 ===\n");

    // 简单的内存分配测试
    void *test_ptr = malloc(1024);
    if (!test_ptr) {
        TLOGE("❌ 内存分配失败\n");
        return ERR_NO_MEMORY;
    }

    // 写入测试数据
    memset(test_ptr, 0xAA, 1024);

    // 验证数据
    unsigned char *data = (unsigned char *)test_ptr;
    for (int i = 0; i < 1024; i++) {
        if (data[i] != 0xAA) {
            TLOGE("❌ 内存数据验证失败\n");
            free(test_ptr);
            return ERR_GENERIC;
        }
    }

    free(test_ptr);
    TLOGI("✅ 基本功能测试通过\n");
    return TEE_SUCCESS;
}

static int test_memory_functionality(void) {
    TLOGI("=== 测试内存管理功能 ===\n");

    // 测试多次内存分配和释放
    for (int i = 0; i < 10; i++) {
        size_t size = 512 + i * 64;
        void *ptr = malloc(size);
        if (!ptr) {
            TLOGE("❌ 内存分配失败，大小: %zu\n", size);
            return ERR_NO_MEMORY;
        }

        // 写入测试模式
        memset(ptr, 0x55 + i, size);

        // 验证数据
        unsigned char *data = (unsigned char *)ptr;
        unsigned char expected = 0x55 + i;
        for (size_t j = 0; j < size; j++) {
            if (data[j] != expected) {
                TLOGE("❌ 内存数据验证失败\n");
                free(ptr);
                return ERR_GENERIC;
            }
        }

        free(ptr);
    }

    TLOGI("✅ 内存管理功能测试通过\n");
    return TEE_SUCCESS;
}

static int test_crypto_placeholder(void) {
    TLOGI("=== 测试加密功能占位符 ===\n");

    // 简单的数据处理测试，模拟加密操作
    unsigned char test_data[32];
    unsigned char processed_data[32];

    // 初始化测试数据
    for (int i = 0; i < 32; i++) {
        test_data[i] = i;
    }

    // 简单的"加密"操作（XOR）
    for (int i = 0; i < 32; i++) {
        processed_data[i] = test_data[i] ^ 0xAA;
    }

    // "解密"操作
    for (int i = 0; i < 32; i++) {
        processed_data[i] = processed_data[i] ^ 0xAA;
    }

    // 验证数据
    if (memcmp(test_data, processed_data, 32) != 0) {
        TLOGE("❌ 数据处理验证失败\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ 加密功能占位符测试通过\n");
    TLOGI("注意: 这是占位符测试，Tongsuo库集成后将替换为真实的加密测试\n");
    return TEE_SUCCESS;
}

// rctee TA命令处理函数
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size) {

    TLOGI("Tongsuo测试TA命令调用: %u\n", cmd);

    switch (cmd) {
    case TEE_TEST_CMD_BASIC_TEST:
        TLOGI("处理基本测试命令\n");
        return test_basic_functionality();

    case TEE_TEST_CMD_MEMORY_TEST:
        TLOGI("处理内存测试命令\n");
        return test_memory_functionality();

    case TEE_TEST_CMD_CRYPTO_TEST:
        TLOGI("处理加密测试命令\n");
        return test_crypto_placeholder();

    default:
        TLOGE("未知命令: %u\n", cmd);
        return ERR_INVALID_ARGS;
    }
}

// rctee TA入口点函数
int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo测试TA客户端连接\n");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    TLOGI("Tongsuo测试TA客户端断开连接\n");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo测试TA初始化\n");
    return TEE_SUCCESS;
}

// 注意: main函数由libutee库提供，这里不需要定义
